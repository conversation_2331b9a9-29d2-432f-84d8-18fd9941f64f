/*!
 * @file
 * @brief Hardware Timer-based time source implementation for HC32L186 MCU.
 *
 * Copyright xx - Confidential - All rights reserved
 */

#ifndef HC32L186_TIMER_TIMESOURCE_H
#define HC32L186_TIMER_TIMESOURCE_H

#include "I_TimeSource.h"

/*!
 * Timer channels available on HC32L186
 */
typedef enum
{
   HC32L186_TIMER_TIM0 = 0,
   HC32L186_TIMER_TIM1 = 1,
   HC32L186_TIMER_TIM2 = 2,
   HC32L186_TIMER_TIM3 = 3
} HC32L186_TimerChannel_t;

/*!
 * Hardware Timer time source instance for HC32L186.
 */
typedef struct
{
   I_TimeSource_t interface;
   
   struct
   {
      volatile TimeSourceTickCount_t tickCount;
      HC32L186_TimerChannel_t timerChannel;
      uint32_t tickFrequencyHz;
   } _private;
} HC32L186_TimerTimeSource_t;

/*!
 * Initialize the Timer time source.
 * @param instance The time source instance.
 * @param timerChannel Timer channel to use (TIM0-TIM3).
 * @param systemClockHz System clock frequency in Hz.
 * @param tickFrequencyHz Desired tick frequency in Hz.
 */
void HC32L186_TimerTimeSource_Init(
   HC32L186_TimerTimeSource_t *instance,
   HC32L186_TimerChannel_t timerChannel,
   uint32_t systemClockHz,
   uint32_t tickFrequencyHz);

/*!
 * Timer interrupt handler - must be called from appropriate TIMx_IRQHandler().
 * @param instance The time source instance.
 */
void HC32L186_TimerTimeSource_IRQHandler(HC32L186_TimerTimeSource_t *instance);

#endif
