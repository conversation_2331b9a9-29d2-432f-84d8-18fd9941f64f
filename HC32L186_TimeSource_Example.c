/*!
 * @file
 * @brief Usage example for HC32L186 TimeSource implementations.
 *
 * Copyright xx - Confidential - All rights reserved
 */

#include "HC32L186_SysTickTimeSource.h"
#include "HC32L186_TimerTimeSource.h"
#include "Timer.h"

// Global time source instances
static HC32L186_SysTickTimeSource_t sysTickTimeSource;
static HC32L186_TimerTimeSource_t timerTimeSource;

// Timer module and timer instances
static TimerModule_t timerModule;
static Timer_t myTimer;

// Example callback function
static void MyTimerCallback(void *context)
{
   // Timer expired - do something
   // For example, toggle an LED
   // GPIO_TogglePin(LED_PORT, LED_PIN);
}

// Example 1: Using SysTick-based time source
void Example_SysTickTimeSource(void)
{
   // Initialize SysTick time source with 1ms ticks
   // Assuming 48MHz system clock
   HC32L186_SysTickTimeSource_Init(&sysTickTimeSource, 48000000, 1000);
   
   // Initialize timer module with SysTick time source
   TimerModule_Init(&timerModule, (I_TimeSource_t*)&sysTickTimeSource);
   
   // Start a 500ms one-shot timer
   TimerModule_StartOneShot(&timerModule, &myTimer, 500, MyTimerCallback, NULL);
   
   // Main loop
   while(1)
   {
      // Run timer module - should be called regularly
      TimerModule_Run(&timerModule);
      
      // Other application code...
   }
}

// Example 2: Using Hardware Timer-based time source
void Example_HardwareTimerTimeSource(void)
{
   // Initialize Timer0-based time source with 1ms ticks
   // Assuming 48MHz system clock
   HC32L186_TimerTimeSource_Init(&timerTimeSource, HC32L186_TIMER_TIM0, 48000000, 1000);
   
   // Initialize timer module with hardware timer time source
   TimerModule_Init(&timerModule, (I_TimeSource_t*)&timerTimeSource);
   
   // Start a 1000ms periodic timer
   TimerModule_StartPeriodic(&timerModule, &myTimer, 1000, MyTimerCallback, NULL);
   
   // Main loop
   while(1)
   {
      // Run timer module
      TimerModule_Run(&timerModule);
      
      // Other application code...
   }
}

// Interrupt handlers that must be implemented in your main.c or interrupt handlers file

// SysTick interrupt handler
void SysTick_Handler(void)
{
   HC32L186_SysTickTimeSource_IRQHandler(&sysTickTimeSource);
}

// Timer0 interrupt handler (if using hardware timer)
void TIM0_IRQHandler(void)
{
   HC32L186_TimerTimeSource_IRQHandler(&timerTimeSource);
}

// Additional timer interrupt handlers as needed
void TIM1_IRQHandler(void)
{
   // If using TIM1 for time source
   // HC32L186_TimerTimeSource_IRQHandler(&timerTimeSource);
}

void TIM2_IRQHandler(void)
{
   // If using TIM2 for time source
   // HC32L186_TimerTimeSource_IRQHandler(&timerTimeSource);
}

void TIM3_IRQHandler(void)
{
   // If using TIM3 for time source
   // HC32L186_TimerTimeSource_IRQHandler(&timerTimeSource);
}
