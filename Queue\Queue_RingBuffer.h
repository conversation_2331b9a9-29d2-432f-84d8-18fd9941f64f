/*!
 * @file
 * @brief An implementation of a queue that uses a ring buffer
 *
 * Copyright xx - Confidential - All rights reserved.
 */

#ifndef QUEUE_RINGBUFFER_H
#define QUEUE_RINGBUFFER_H

#include "I_Queue.h"
#include "RingBuffer.h"

typedef struct
{
   I_Queue_t interface;

   struct
   {
      uint16_t numberOfElements;
      RingBuffer_t ringBuffer;
   } _private;
} Queue_RingBuffer_t;

/*!
 * Initializes the queue
 * @param instance The instance
 * @param storage The space that the queue can use
 * @param storageSize The number of bytes in the allotted storage
 */
void Queue_RingBuffer_Init(
   Queue_RingBuffer_t *instance,
   void *storage,
   size_t storageSize);

#endif
