/*!
 * @file
 * @brief Hardware Timer-based time source implementation for HC32L186 MCU.
 *
 * Copyright xx - Confidential - All rights reserved
 */

#include "HC32L186_TimerTimeSource.h"
#include "hc32l186.h"  // HC32L186 HAL header
#include "uassert.h"

// Forward declaration
static TimeSourceTickCount_t GetTicks(I_TimeSource_t *instance);

// API table
static const I_TimeSource_Api_t api = {
   .GetTicks = GetTicks
};

// Timer base addresses (adjust according to HC32L186 memory map)
static TIM_TypeDef* const TimerBases[] = {
   TIM0,  // Assuming these are defined in hc32l186.h
   TIM1,
   TIM2,
   TIM3
};

static TimeSourceTickCount_t GetTicks(I_TimeSource_t *_instance)
{
   HC32L186_TimerTimeSource_t *instance = (HC32L186_TimerTimeSource_t *)_instance;
   uassert(instance);
   
   return instance->_private.tickCount;
}

void HC32L186_TimerTimeSource_Init(
   HC32L186_TimerTimeSource_t *instance,
   HC32L186_TimerChannel_t timerChannel,
   uint32_t systemClockHz,
   uint32_t tickFrequencyHz)
{
   uassert(instance);
   uassert(timerChannel < 4);
   uassert(systemClockHz > 0);
   uassert(tickFrequencyHz > 0);
   
   // Initialize interface
   instance->interface.api = &api;
   
   // Initialize private data
   instance->_private.tickCount = 0;
   instance->_private.timerChannel = timerChannel;
   instance->_private.tickFrequencyHz = tickFrequencyHz;
   
   TIM_TypeDef *timer = TimerBases[timerChannel];
   
   // Enable timer clock (implementation depends on HC32L186 clock system)
   // This is pseudo-code - adjust according to actual HC32L186 HAL
   switch(timerChannel)
   {
      case HC32L186_TIMER_TIM0:
         // Enable TIM0 clock
         break;
      case HC32L186_TIMER_TIM1:
         // Enable TIM1 clock
         break;
      case HC32L186_TIMER_TIM2:
         // Enable TIM2 clock
         break;
      case HC32L186_TIMER_TIM3:
         // Enable TIM3 clock
         break;
   }
   
   // Calculate timer period
   uint32_t timerPeriod = (systemClockHz / tickFrequencyHz) - 1;
   
   // Configure timer (pseudo-code - adjust for actual HC32L186 registers)
   timer->ARR = timerPeriod;        // Auto-reload value
   timer->PSC = 0;                  // Prescaler (adjust if needed)
   timer->CNT = 0;                  // Reset counter
   
   // Enable timer interrupt
   timer->DIER |= TIM_DIER_UIE;     // Update interrupt enable
   
   // Enable timer
   timer->CR1 |= TIM_CR1_CEN;       // Counter enable
   
   // Enable NVIC interrupt (adjust IRQ numbers for HC32L186)
   IRQn_Type irqn;
   switch(timerChannel)
   {
      case HC32L186_TIMER_TIM0: irqn = TIM0_IRQn; break;
      case HC32L186_TIMER_TIM1: irqn = TIM1_IRQn; break;
      case HC32L186_TIMER_TIM2: irqn = TIM2_IRQn; break;
      case HC32L186_TIMER_TIM3: irqn = TIM3_IRQn; break;
      default: return;
   }
   
   NVIC_EnableIRQ(irqn);
}

void HC32L186_TimerTimeSource_IRQHandler(HC32L186_TimerTimeSource_t *instance)
{
   uassert(instance);
   
   TIM_TypeDef *timer = TimerBases[instance->_private.timerChannel];
   
   // Check if update interrupt flag is set
   if(timer->SR & TIM_SR_UIF)
   {
      // Clear interrupt flag
      timer->SR &= ~TIM_SR_UIF;
      
      // Increment tick count
      instance->_private.tickCount++;
   }
}
