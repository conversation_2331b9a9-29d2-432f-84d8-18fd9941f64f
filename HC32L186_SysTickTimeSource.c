/*!
 * @file
 * @brief SysTick-based time source implementation for HC32L186 MCU.
 *
 * Copyright xx - Confidential - All rights reserved
 */

#include "HC32L186_SysTickTimeSource.h"
#include "hc32l186.h"  // HC32L186 HAL header
#include "uassert.h"

// Forward declaration
static TimeSourceTickCount_t GetTicks(I_TimeSource_t *instance);

// API table
static const I_TimeSource_Api_t api = {
   .GetTicks = GetTicks
};

static TimeSourceTickCount_t GetTicks(I_TimeSource_t *_instance)
{
   HC32L186_SysTickTimeSource_t *instance = (HC32L186_SysTickTimeSource_t *)_instance;
   uassert(instance);
   
   // Return current tick count (atomic read on Cortex-M0+)
   return instance->_private.tickCount;
}

void HC32L186_SysTickTimeSource_Init(
   HC32L186_SysTickTimeSource_t *instance,
   uint32_t systemClockHz,
   uint32_t tickFrequencyHz)
{
   uassert(instance);
   uassert(systemClockHz > 0);
   uassert(tickFrequencyHz > 0);
   uassert(tickFrequencyHz <= systemClockHz);
   
   // Initialize interface
   instance->interface.api = &api;
   
   // Initialize private data
   instance->_private.tickCount = 0;
   instance->_private.tickFrequencyHz = tickFrequencyHz;
   
   // Calculate SysTick reload value
   uint32_t reloadValue = (systemClockHz / tickFrequencyHz) - 1;
   uassert(reloadValue <= 0xFFFFFF); // SysTick is 24-bit
   
   // Configure SysTick
   SysTick->LOAD = reloadValue;
   SysTick->VAL = 0;
   SysTick->CTRL = SysTick_CTRL_CLKSOURCE_Msk |  // Use processor clock
                   SysTick_CTRL_TICKINT_Msk |    // Enable interrupt
                   SysTick_CTRL_ENABLE_Msk;      // Enable SysTick
}

void HC32L186_SysTickTimeSource_IRQHandler(HC32L186_SysTickTimeSource_t *instance)
{
   uassert(instance);
   
   // Increment tick count
   instance->_private.tickCount++;
   
   // Handle overflow (optional - depends on your requirements)
   // Since TimeSourceTickCount_t is uint16_t, it will naturally wrap at 65536
}
