/*!
 * @file
 * @brief SysTick-based time source implementation for HC32L186 MCU.
 *
 * Copyright xx - Confidential - All rights reserved
 */

#ifndef HC32L186_SYSTICK_TIMESOURCE_H
#define HC32L186_SYSTICK_TIMESOURCE_H

#include "I_TimeSource.h"

/*!
 * SysTick time source instance for HC32L186.
 */
typedef struct
{
   I_TimeSource_t interface;
   
   struct
   {
      volatile TimeSourceTickCount_t tickCount;
      uint32_t tickFrequencyHz;
   } _private;
} HC32L186_SysTickTimeSource_t;

/*!
 * Initialize the SysTick time source.
 * @param instance The time source instance.
 * @param systemClockHz System clock frequency in Hz.
 * @param tickFrequencyHz Desired tick frequency in Hz (e.g., 1000 for 1ms ticks).
 */
void HC32L186_SysTickTimeSource_Init(
   HC32L186_SysTickTimeSource_t *instance,
   uint32_t systemClockHz,
   uint32_t tickFrequencyHz);

/*!
 * SysTick interrupt handler - must be called from SysTick_Handler().
 * @param instance The time source instance.
 */
void HC32L186_SysTickTimeSource_IRQHandler(HC32L186_SysTickTimeSource_t *instance);

#endif
